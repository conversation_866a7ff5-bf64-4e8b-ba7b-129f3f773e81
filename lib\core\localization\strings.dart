// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: constant_identifier_names

abstract class  LocaleKeys {
  static const login_to_your_account = 'login_to_your_account';
  static const welcome_back_login_to_continue = 'welcome_back_login_to_continue';
  static const write_your_phone_number = 'write_your_phone_number';
  static const write_password = 'write_password';
  static const login = 'login';
  static const forget_password_question = 'forget_password_question';
  static const or = 'or';
  static const enter_as_a_guest = 'enter_as_a_guest';
  static const become_a_provider = 'become_a_provider';
  static const dont_have_an_account = 'dont_have_an_account';
  static const register_now = 'register_now';
  static const sign_up_to_create_an_account = 'sign_up_to_create_an_account';
  static const first_name = 'first_name';
  static const last_name = 'last_name';
  static const state = 'state';
  static const city = 'city';
  static const confirm_password = 'confirm_password';
  static const i_agree_with = 'i_agree_with';
  static const terms_and_policies = 'terms_and_policies';
  static const sign_up = 'sign_up';
  static const already_have_an_account = 'already_have_an_account';
  static const login_now = 'login_now';
  static const this_field_is_required = 'this_field_is_required';
  static const phone_number_must_be_at_least_10_digits = 'phone_number_must_be_at_least_10_digits';
  static const password_validation_message = 'password_validation_message';
  static const passwords_do_not_match = 'passwords_do_not_match';
  static const verify_your_phone_number = 'verify_your_phone_number';
  static const enter_the_four_digits_otp_sent_to = 'enter_the_four_digits_otp_sent_to';
  static const verify = 'verify';
  static const didnt_receive_code = 'didnt_receive_code';
  static const resend_code = 'resend_code';
  static const forget_password = 'forget_password';
  static const enter_your_phone_number_to_receive_the_code = 'enter_your_phone_number_to_receive_the_code';
  static const send_code = 'send_code';
  static const change_password = 'change_password';
  static const create_your_new_password_to_access_your_account = 'create_your_new_password_to_access_your_account';
  static const old_password = 'old_password';
  static const new_password = 'new_password';
  static const reset_password = 'reset_password';
  static const home = 'home';
  static const offers = 'offers';
  static const providers = 'providers';
  static const entities = 'entities';
  static const settings = 'settings';
  static const offers_list = 'offers_list';
  static const service_providers = 'service_providers';
  static const government_entities = 'government_entities';
  static const categories = 'categories';
  static const show_all = 'show_all';
  static const best = 'best';
  static const just_for_you = 'just_for_you';
  static const n_views = 'n_views';
  static const edit_profile = 'edit_profile';
  static const about_the_app = 'about_the_app';
  static const privacy_policy = 'privacy_policy';
  static const contact_us = 'contact_us';
  static const about_us = 'about_us';
  static const language = 'language';
  static const details = 'details';
  static const gallery = 'gallery';
  static const description = 'description';
  static const contact_details = 'contact_details';
  static const search = 'search';
  static const category = 'category';
  static const subcategory = 'subcategory';
  static const none = 'none';
  static const reset = 'reset';
  static const apply = 'apply';
  static const filters = 'filters';
  static const success = 'success';
  static const warning = 'warning';
  static const failure = 'failure';
  static const retry = 'retry';
  static const click_to_refresh = 'click_to_refresh';
  static const welcome_guest = 'welcome_guest';
  static const phone_number = 'phone_number';
  static const facebook = 'facebook';
  static const instagram = 'instagram';

}
