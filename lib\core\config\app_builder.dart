import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';

import '../localization/localization.dart';
import '../models/user/general_user.dart';
import '../routes/navigation.dart';
import '../style/themes.dart';
import '../widgets/general/loading/loading.dart';
import 'defaults.dart';
import 'role.dart';

const _kAppStorage = "app";

class AppBuilder extends GetxService {
  //SECTION - Data stored in storage
  Role role = Default.defaultRole;
  String? token;
  ValueNotifier<GeneralUser?> user = ValueNotifier(null);

  //!SECTION

  //SECTION - Storages boxes

  GetStorage box = GetStorage(_kAppStorage);
  final secureStorage = const FlutterSecureStorage();

  //SECTION - Load Data

  loadUserData() async {
    await box.initStorage;

    if (!box.hasData("theme")) {
      setTheme(Default.defaultTheme);
    } else {
      updateTheme(AppTheme.fromString(box.read("theme")));
    }

    if (box.hasData("role")) {
      role = Role.fromString(box.read("role"));
    }
    if (box.hasData("user")) {
      if (role is User) {
        user.value = GeneralUser.fromRawJson(box.read("user"));
      }
    }

    Map<String, dynamic> data = await secureStorage.readAll();
    if (data.keys.contains("token")) {
      token = data["token"];
    }
    logData();
  }

  logData() {
    log('role: $role', name: "APP BUILDER");
    log('locale: $currentLocale', name: "APP BUILDER");
    log('theme: $theme', name: "APP BUILDER");
    log('token: $token', name: "APP BUILDER");
    log('user: ${user.value?.toJson()}', name: "APP BUILDER");
  }

  //!SECTION

  //SECTION - Set Data

  ///Used to set user data when login or register
  setUserData({required Role role, required String token, GeneralUser? user}) {
    setRole(role);
    setToken(token);
    if (user != null) setUser(user);
  }

  setTheme(AppTheme theme) {
    box.write("theme", theme.name);
    this.theme = theme;
  }

  ///Change user role and store in storage
  setRole(Role role) {
    box.write("role", role.toString());
    this.role = role;
  }

  setToken(String? token) {
    if (token == null) {
      secureStorage.delete(key: "token");
      this.token = null;
      return;
    }
    secureStorage.write(key: "token", value: token);
    this.token = token;

    Get.find<APIService>().setToken(token);
  }

  setUser(GeneralUser? user) {
    if (user == null) {
      box.write("user", null);
      this.user.value = null;
      return;
    }
    box.write("user", user.toRawJson());

    this.user.value = user;
  }

  ///Used to reset user data and clean up the storage
  logout({bool withAPI = true}) async {
    setRole(const UnregisteredUser());
    setToken(null);
    setUser(null);

    if (withAPI) {
      Loading.show();
      await APIService.instance.requestAPI(
        Request(endPoint: EndPoints.logout, method: RequestMethod.Delete),
      );
      Loading.dispose();
    }

    Nav.offUntil(role.landing, (_) => false);
  }

  //!SECTION
  //!SECTION

  //SECTION - Localization
  changeLocale({BuildContext? context, required AppLocalization locale}) {
    (context ?? Get.context!).setLocale(locale.locale);
    Get.updateLocale(locale.locale);
    Get.find<APIService>().setLanguage(locale.value);
  }

  updateLocale() => changeLocale(
    locale: currentLocale.isEnglish ? AppLocalization.ar : AppLocalization.en,
  );

  AppLocalization get currentLocale => AppLocalization.fromString(
    EasyLocalization.of(Get.context!)!.currentLocale!.languageCode,
  );
  //!SECTION

  //SECTION - Theme
  final Rx<AppTheme> _theme = Default.defaultTheme.obs;
  AppTheme get theme => _theme.value;
  set theme(AppTheme value) => _theme.value = value;

  updateTheme(AppTheme theme) {
    setTheme(theme);
  }
  //!SECTION

  //SECTION - Initialize app

  ///Initializer for the application
  ///you have to do navigation here
  init() async {
    await loadUserData();
    Get.put(
      APIService(
        token: token,
        language: currentLocale.value,
        headers: {"Accept": "application/json"},
        withLog: false,
      ),
    );

    //TODO - Activate any global service you need

    // bool willFirebaseDoNavigation = await FirebaseMessagingService.init();
    // await FirebaseMessagingService.getToken();
    // bool willLocalNotificationDoNavigation =
    //     await LocalNotificationService.init();

    // bool willDoNavigation =
    //     willFirebaseDoNavigation || willLocalNotificationDoNavigation;

    //NOTE - Here you can activate anyservices depends on user role
    await role.initialize();

    // if (willDoNavigation) return;
    return Nav.offUntil(role.landing, (_) => false);
  }

  //!SECTION
}
