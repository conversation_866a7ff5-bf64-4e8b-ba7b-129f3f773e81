import 'filter_result.dart';

class FiltersParams {
  final FiltersActivations activations;
  final InitialFilters initialFilters;

  FiltersParams({
    this.activations = const FiltersActivations(),
    this.initialFilters = const InitialFilters(),
  });
}

class FiltersActivations {
  final bool category, subcategory, state, city;

  const FiltersActivations({
    this.category = true,
    this.subcategory = true,
    this.state = true,
    this.city = true,
  });
}

class InitialFilters {
  final int? categoryId, subcategoryId, stateId, cityId;

  const InitialFilters({
    this.categoryId,
    this.subcategoryId,
    this.stateId,
    this.cityId,
  });

  FiltersResult toFiltersResult() => FiltersResult(
    categoryId: categoryId,
    subcategoryId: subcategoryId,
    stateId: stateId,
    cityId: cityId,
  );
}
