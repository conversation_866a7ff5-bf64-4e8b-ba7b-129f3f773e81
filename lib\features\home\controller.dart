import 'package:get/get.dart';
import 'package:tredo/core/models/goverment_entity/goverment_entity.dart';
import 'package:tredo/core/models/offer/offer.dart';
import 'package:tredo/core/models/provider/main_provider.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/services/state_management/obs.dart';
import 'package:tredo/features/providers/provider_details/models/nav.dart';

import '../../core/models/category/category.dart';
import 'models/ad.dart';

class HomePageController extends GetxController {
  @override
  void onInit() {
    fetchAds();
    fetchCategories();
    fetchEntities();
    fetchProviders();
    fetchOffers();
    super.onInit();
  }

  Future refreshData() async {
    refreshAds();
    refreshCategories();
    refreshEntities();
    refreshProviders();
    refreshOffers();
  }

  //SECTION - Ads
  ObsList<AdModel> ads = ObsList([]);
  fetchAds() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.ads, fromJson: AdModel.fromJson),
    );
    if (response.success) {
      ads.value = response.data;
    } else {
      ads.error = response.message;
    }
  }

  refreshAds() async {
    ads.reset();
    fetchAds();
  }

  clickAd(AdModel ad) {
    if (ad.providerId != null) {
      Nav.to(
        Pages.provider,
        arguments: ProviderDetailsPageNav(providerId: ad.providerId!),
      );
    }
  }
  //!SECTION

  //SECTION - Categories
  ObsList<Category> categories = ObsList([]);
  fetchCategories() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.categories,
        fromJson: Category.fromJson,
        params: {"page": 1},
      ),
    );
    if (response.success) {
      categories.value = response.data;
    } else {
      categories.error = response.message;
    }
  }

  refreshCategories() async {
    categories.reset();
    fetchCategories();
  }

  //!SECTION

  //SECTION - Goverment Entities
  ObsList<GovermentEntity> entities = ObsList([]);
  fetchEntities() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.entities,
        fromJson: GovermentEntity.fromJson,
        params: {"page": 1},
      ),
    );
    if (response.success) {
      entities.value = response.data;
    } else {
      entities.error = response.message;
    }
  }

  refreshEntities() async {
    entities.reset();
    fetchEntities();
  }

  //!SECTION

  //SECTION - Offers
  ObsList<Offer> offers = ObsList([]);
  fetchOffers() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.offers,
        fromJson: Offer.fromJson,
        params: {"page": 1},
      ),
    );
    if (response.success) {
      offers.value = response.data;
    } else {
      offers.error = response.message;
    }
  }

  refreshOffers() async {
    offers.reset();
    fetchOffers();
  }

  //!SECTION

  //SECTION - Providers
  ObsList<MainProvider> providers = ObsList([]);
  fetchProviders() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.providers,
        fromJson: MainProvider.fromJson,
        params: {"page": 1},
      ),
    );
    if (response.success) {
      providers.value = response.data;
    } else {
      providers.error = response.message;
    }
  }

  refreshProviders() async {
    providers.reset();
    fetchProviders();
  }

  //!SECTION
}
