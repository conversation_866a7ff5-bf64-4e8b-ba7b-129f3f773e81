import 'package:flutter/widgets.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:tredo/core/constants/controllers_tags.dart';
import 'package:tredo/core/models/image.dart';
import 'package:tredo/core/services/pagination/options/grid_view.dart';
import 'package:tredo/core/widgets/general/loading/shimmer_loading.dart';
import 'package:tredo/core/widgets/image.dart';
import 'package:tredo/features/offers/widgets/offer_card/offer_card.dart';
import 'package:tredo/features/offers/widgets/offer_card/style/style.dart';

import '../../controller.dart';

class OffersTab extends GetView<ProviderDetailsPageController> {
  const OffersTab({super.key});

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.offerCardTheme;
    return GridViewPagination.alignedSliver(
      scrollController: controller.scrollController,
      tag: ControllersTags.provider_offers_pager,
      fetchApi: controller.fetchOffers,
      fromJson: (json) => ImageModel.fromJson(json['media']["image"].first),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      crossAxisCount: 2,
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      loading: AspectRatio(
        aspectRatio: OfferCard.aspectRatio,
        child: ShimmerWidget(borderRadius: BorderRadius.circular(4)),
      ),
      initialLoading: SliverPadding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        sliver: SliverAlignedGrid.count(
          crossAxisCount: 2,
          mainAxisSpacing: 12,
          crossAxisSpacing: 12,
          itemBuilder:
              (_, __) => AspectRatio(
                aspectRatio: OfferCard.aspectRatio,
                child: ShimmerWidget(borderRadius: BorderRadius.circular(4)),
              ),
        ),
      ),
      itemBuilder:
          (context, index, image) => AspectRatio(
            aspectRatio: OfferCard.aspectRatio,
            child: AppImage(
              path: image.large,
              decoration: BoxDecoration(
                boxShadow: cardTheme.shadow,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
    );
  }
}
