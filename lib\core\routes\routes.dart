// ignore_for_file: constant_identifier_names

import 'package:get/route_manager.dart';
import 'package:tredo/core/widgets/general/pages/coming_soon.dart';
import 'package:tredo/features/categories/categories/binding.dart';
import 'package:tredo/features/categories/categories/index.dart';
import 'package:tredo/features/main/binding.dart';
import 'package:tredo/features/providers/create_provider/binding.dart';
import 'package:tredo/features/providers/create_provider/index.dart';
import 'package:tredo/features/providers/provider_details/binding.dart';
import 'package:tredo/features/providers/provider_details/index.dart';
import '../../features/auth/forget_password/binding.dart';
import '../../features/auth/forget_password/index.dart';
import '../../features/auth/login/binding.dart';
import '../../features/auth/login/index.dart';
import '../../features/auth/register/binding.dart';
import '../../features/auth/register/index.dart';
import '../../features/auth/reset_password/binding.dart';
import '../../features/auth/reset_password/index.dart';
import '../../features/auth/verification/binding.dart';
import '../../features/auth/verification/index.dart';
import '../../features/main/index.dart';
import '../../features/splash_screen/index.dart';
import '../config/role_middleware.dart';

abstract class AppRouting {
  static GetPage unknownRoute = GetPage(
    name: "/coming_soon",
    page: () => const ComingSoonPage(),
  );

  static GetPage initialroute = GetPage(
    name: "/",
    page: () => const SplashScreen(),
  );

  static List<GetPage<dynamic>> routes() => [
    unknownRoute,
    initialroute,
    ...Pages.values.map((page) => page.page),
  ];
}

enum Pages {
  //auth
  onboarding,
  login,
  register,
  forget_password,
  verification,
  reset_password,

  //home
  home,

  //providers
  provider,
  create_provider,

  //categories
  categories,

  //settnigs
  terms;

  String get value => '/$name';

  GetPage get page => switch (this) {
    login => GetPage(
      name: value,
      binding: LoginPageBinding(),
      page: () => const LoginPage(),
    ),
    register => GetPage(
      name: value,
      binding: RegisterPageBinding(),
      page: () => const RegisterPage(),
    ),
    verification => GetPage(
      name: value,
      binding: VerificationPageBinding(),
      page: () => const VerificationPage(),
    ),
    forget_password => GetPage(
      name: value,
      binding: ForgetPasswordPageBinding(),
      page: () => const ForgetPasswordPage(),
    ),
    reset_password => GetPage(
      name: value,
      binding: ResetPasswordPageBinding(),
      page: () => const ResetPasswordPage(),
    ),
    home => GetPage(
      name: value,
      binding: MainPageBinding(),
      page: () => const MainPage(),
    ),
    categories => GetPage(
      name: value,
      binding: CategoriesPageBinding(),
      page: () => const CategoriesPage(),
    ),
    provider => GetPage(
      name: value,
      binding: ProviderDetailsPageBinding(),
      middlewares: [BlockGuest()],
      page: () => const ProviderDetailsPage(),
    ),
    create_provider => GetPage(
      name: value,
      binding: CreateProviderPageBinding(),
      page: () => const CreateProviderPage(),
    ),
    _ => AppRouting.unknownRoute,
  };
}
