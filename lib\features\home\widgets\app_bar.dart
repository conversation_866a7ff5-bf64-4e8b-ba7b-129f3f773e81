import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/config/app_builder.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/core/widgets/image.dart';

import '../controller.dart';

class HomeAppBar extends GetView<HomePageController> {
  const HomeAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        16,
        MediaQuery.paddingOf(context).top + 24,
        16,
        24,
      ),
      child: ValueListenableBuilder(
        valueListenable: Get.find<AppBuilder>().user,
        builder: (context, user, _) {
          if (user == null) {
            return Text(
              tr(LocaleKeys.welcome_guest),
              textAlign: TextAlign.start,
              style: context.textTheme.titleMedium,
            );
          }
          return Row(
            children: [
              AppImage(
                path: user.image,
                type: ImageType.CachedNetwork,
                height: 50,
                width: 50,
                decoration: const BoxDecoration(shape: BoxShape.circle),
              ),
              const Gap(12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${user.firstName} ${user.lastName}",
                      style: context.textTheme.titleMedium,
                    ),
                    Text(
                      "${user.state.name}, ${user.city.name}",
                      style: context.textTheme.labelMedium,
                    ),
                  ],
                ),
              ),
              const Gap(12),
              IconButton(
                onPressed: () {},
                icon: SvgIcon(Assets.icons.notifications),
              ),
            ],
          );
        },
      ),
    );
  }
}
