import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/error/error_card.dart';
import 'package:tredo/features/categories/widgets/category_card/category_card.dart';

import '../controller.dart';

class CategoriesList extends GetView<HomePageController> {
  const CategoriesList({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  tr(LocaleKeys.categories),
                  style: context.textTheme.titleLarge,
                ),
              ),
              TextButton(
                onPressed: () => Nav.to(Pages.categories),
                child: Text(
                  tr(LocaleKeys.show_all),
                  style: context.textTheme.bodyMedium!.copyWith(
                    color: context.appColorScheme.secondaryColor.shade700,
                  ),
                ),
              ),
            ],
          ),
        ),
        const Gap(16),
        ObsListBuilder(
          obs: controller.categories,
          loader:
              (context) => SizedBox(
                height: 100,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: 10,
                  separatorBuilder: (_, __) => const Gap(12),
                  itemBuilder: (context, index) => const CategoryCardLoading(),
                ),
              ),
          errorBuilder:
              (context, error) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SizedBox(
                  height: 100,
                  child: ErrorCard(
                    error: error,
                    onRefresh: controller.refreshCategories,
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
              ),
          builder: (context, categories) {
            return SizedBox(
              height: 100,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: categories.length,
                separatorBuilder: (_, __) => const Gap(12),
                itemBuilder:
                    (context, index) =>
                        CategoryCard(category: categories[index]),
              ),
            );
          },
        ),
      ],
    );
  }
}
