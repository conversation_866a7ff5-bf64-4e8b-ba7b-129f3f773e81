import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/widgets/general/toast/toast.dart';
import 'package:tredo/core/widgets/general/loading/loading.dart';
import 'package:tredo/features/auth/reset_password/models/nav.dart';
import 'package:tredo/features/auth/verification/models/nav.dart';

class ForgetPasswordPageController extends GetxController {
  late TextEditingController phone;

  @override
  void onInit() {
    phone = TextEditingController();
    super.onInit();
  }

  @override
  void onClose() {
    phone.dispose();
    super.onClose();
  }

  final Rx<bool> _isPhoneValid = false.obs;
  bool get isPhoneValid => _isPhoneValid.value;
  set isPhoneValid(bool value) => _isPhoneValid.value = value;

  sendCode() async {
    Loading.show();

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.forget_password,
        method: RequestMethod.Post,
        body: {"phone": phone.text},
      ),
    );

    Loading.dispose();

    if (response.success) {
      String? code = await Nav.to(
        Pages.verification,
        arguments: VerificationPageNav(
          phone: phone.text,
          isForVerifyAccount: false,
        ),
      );

      if (code != null) {
        bool? result = await Nav.to(
          Pages.reset_password,
          arguments: ResetPasswordPageNav(
            mode: ResetPasswordMode.reset,
            phone: phone.text,
            code: code,
          ),
        );

        if (result ?? false) {
          //TODO - success message
          Nav.offUntil(Pages.login, (_) => false);
        }
      }
    } else {
      Toast.show(message: response.message);
    }
  }
}
