import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tredo/core/utils/image_utils.dart';

class CreateProviderPageController extends GetxController {
  final formKey = GlobalKey<FormState>();

  late TextEditingController providerName,
      shopName,
      description,
      phone,
      whatsapp,
      instagram,
      facebook;

  int? stateId, cityId, categoryId, subcategoryId;

  final Rx<String> _image = "".obs;
  String get image => _image.value;
  set image(String value) => _image.value = value;

  pickImage() async {
    final result = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (result == null) return;

    final compressed = await ImageUtils.compressAndGetFile(File(result.path));

    image = compressed ?? result.path;
  }

  @override
  void onInit() {
    providerName = TextEditingController();
    shopName = TextEditingController();
    description = TextEditingController();
    phone = TextEditingController();
    whatsapp = TextEditingController();
    instagram = TextEditingController();
    facebook = TextEditingController();
    super.onInit();
  }

  @override
  void onClose() {
    providerName.dispose();
    shopName.dispose();
    description.dispose();
    phone.dispose();
    whatsapp.dispose();
    instagram.dispose();
    facebook.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) return;
  }
}
