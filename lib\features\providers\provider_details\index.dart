import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/core/widgets/general/tab_bar.dart';
import 'package:tredo/features/categories/widgets/category_card/style/style.dart';

import 'widgets/app_bar.dart';
import 'controller.dart';
import 'widgets/error.dart';
import 'widgets/loading.dart';
import 'widgets/tabs/details.dart';
import 'widgets/tabs/gallery.dart';
import 'widgets/tabs/offers.dart';

class ProviderDetailsPage extends GetView<ProviderDetailsPageController> {
  const ProviderDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final categoryCardTheme = context.categoryCardTheme;

    return Scaffold(
      body: ObsVariableBuilder(
        obs: controller.provider,
        loader: (context) => const ProviderDetailsPageLoading(),
        errorBuilder:
            (context, error) => ProviderDetailsPageError(error: error),
        builder: (context, provider) {
          return CustomScrollView(
            controller: controller.scrollController,
            slivers: [
              ProviderAppBar(backgroundImage: provider.cover?.large),
              SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    Text(
                      provider.shopName,
                      style: context.textTheme.headlineSmall,
                    ),
                    const Gap(8),
                    Row(
                      children: [
                        Container(
                          height: 35,
                          width: 35,
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: categoryCardTheme.backgroundColor,
                            shape: BoxShape.circle,
                          ),
                          child: SvgIconString(
                            provider.category.svgImage,
                            color: categoryCardTheme.iconColor,
                          ),
                        ),
                        const Gap(8),
                        Expanded(
                          child: Text(
                            provider.category.name,
                            style: categoryCardTheme.style,
                          ),
                        ),
                      ],
                    ),
                    const Gap(8),
                    Row(
                      spacing: 16,
                      children: [
                        Row(
                          spacing: 8,
                          children: [
                            SvgIcon(
                              Assets.icons.earth,
                              color: context.appColorScheme.secondaryColor,
                            ),
                            Text(provider.state.name),
                          ],
                        ),
                        Row(
                          spacing: 8,
                          children: [
                            SvgIcon(
                              Assets.icons.building,
                              color: context.appColorScheme.secondaryColor,
                            ),
                            Text(provider.city.name),
                          ],
                        ),
                      ],
                    ),
                  ]),
                ),
              ),
              const SliverGap(16),
              PinnedHeaderSliver(
                child: ColoredBox(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: AppTabBar(
                      initalIndex: controller.nav.initialTab ?? 0,
                      onChanged: (index) => controller.currentTabIndex = index,
                      items: [
                        Text(tr(LocaleKeys.details)),
                        Text(tr(LocaleKeys.gallery)),
                        Text(tr(LocaleKeys.offers)),
                      ],
                    ),
                  ),
                ),
              ),
              Obx(
                () => switch (controller.currentTabIndex) {
                  0 => DetailsTab(provider: provider),
                  1 => GalleryTab(
                    images: provider.gallery.map((e) => e.large).toList(),
                  ),
                  2 => const OffersTab(),
                  _ => SliverList(delegate: SliverChildListDelegate([])),
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
