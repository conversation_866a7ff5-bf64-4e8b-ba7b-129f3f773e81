import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/models/selection.dart';
import 'package:tredo/core/widgets/general/fields/prefix.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';

class AppDropdownField extends StatelessWidget {
  final String? title;
  final Selection? value;
  final SvgGenImage? icon;
  final List<Selection> selections;
  final bool supportNone;
  final String? hint;
  final void Function(Selection? value) onChanged;
  final String? Function(Selection?)? validator;

  const AppDropdownField({
    super.key,
    this.title,
    this.value,
    this.icon,
    this.supportNone = false,
    this.hint,
    this.validator,
    required this.selections,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    Widget field = DropdownButtonFormField<Selection>(
      value: value,
      validator: validator,
      decoration: InputDecoration(
        hintText: hint ?? tr(LocaleKeys.none),
        prefixIcon: FieldPrefix(child: SvgIcon(Assets.icons.categoriesSearch)),
      ),
      items:
          [if (supportNone) Selection.none, ...selections]
              .map((e) => DropdownMenuItem(value: e, child: Text(e.name)))
              .toList(),
      onChanged: (value) {
        if (value == Selection.none) {
          onChanged(null);
        } else {
          onChanged(value);
        }
      },
    );

    if (title != null) {
      field = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8,
        children: [
          Text(
            title!,
            style: context.textTheme.titleMedium!.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          field,
        ],
      );
    }

    return field;
  }
}
