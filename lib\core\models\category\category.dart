class Category {
  int id;
  String name;
  String svgImage;

  Category({required this.id, required this.name, required this.svgImage});

  Category copyWith({int? id, String? name, String? svgImage}) => Category(
    id: id ?? this.id,
    name: name ?? this.name,
    svgImage: svgImage ?? this.svgImage,
  );

  factory Category.fromJson(Map<String, dynamic> json) =>
      Category(id: json["id"], name: json["name"], svgImage: json["svg_image"]);

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "svg_image": svgImage,
  };
}
