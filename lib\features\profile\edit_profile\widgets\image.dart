import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/core/widgets/image.dart';

import '../controller.dart';

class EditProfileImage extends GetView<EditProfilePageController> {
  const EditProfileImage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.image.isEmpty) {
        return InkWell(
          onTap: controller.pickImage,
          child: Container(
            height: 120,
            width: 120,
            padding: const EdgeInsets.all(40),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: context.appColorScheme.secondaryColor.shade100,
            ),
            child: SvgIcon(
              Assets.icons.camera,
              color: context.appColorScheme.secondaryColor,
              size: 40,
            ),
          ),
        );
      } else {
        return Center(
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              AppImage(
                path: controller.image,
                type:
                    controller.image.startsWith("http")
                        ? ImageType.CachedNetwork
                        : ImageType.File,
                height: 120,
                width: 120,
                decoration: const BoxDecoration(shape: BoxShape.circle),
              ),
              Positioned(
                bottom: 0,
                right: 8,
                child: Assets.icons.editBox.svg(),
              ),
            ],
          ),
        );
      }
    });
  }
}
