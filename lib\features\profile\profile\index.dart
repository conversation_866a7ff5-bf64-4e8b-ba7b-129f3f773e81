import 'package:blur/blur.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/config/app_builder.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/core/style/repo.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';
import 'package:tredo/core/widgets/image.dart';
import 'package:tredo/features/profile/profile/widgets/setting_tile.dart';

import 'controller.dart';
import 'widgets/app_bar.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  static const kImageSize = 120.0;

  @override
  Widget build(BuildContext context) {
    Get.put(ProfilePageController());
    AppBuilder appBuilder = Get.find();
    return AppScaffold(
      appBar: const ProfileAppBar(bottomPadding: kImageSize / 2),
      overlay: Positioned(
        top: ProfileAppBar.height + MediaQuery.paddingOf(context).top,
        right: 0,
        left: 0,
        child: Center(
          child: ValueListenableBuilder(
            valueListenable: appBuilder.user,
            builder: (context, user, _) {
              return AppImage(
                path: user?.image ?? "",
                height: kImageSize,
                width: kImageSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: StyleRepo.black.withValues(alpha: .25),
                      blurRadius: 8,
                      offset: const Offset(1, 1),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
      body: Stack(
        children: [
          ListView(
            padding: const EdgeInsets.only(
              top: kImageSize / 2 + 12,
              bottom: 70,
              left: 16,
              right: 16,
            ),
            children: [
              ValueListenableBuilder(
                valueListenable: appBuilder.user,
                builder: (context, user, _) {
                  return Column(
                    children: [
                      Text(
                        "${user?.firstName} ${user?.lastName}",
                        style: context.textTheme.headlineSmall,
                        textAlign: TextAlign.center,
                      ),
                      const Gap(8),
                      Text(
                        "${user?.state.name}, ${user?.city.name}",
                        textAlign: TextAlign.center,
                      ),
                    ],
                  );
                },
              ),
              const Gap(8),
              Align(
                child: OutlinedButton(
                  onPressed: () {},
                  child: Text(tr(LocaleKeys.edit_profile)),
                ),
              ),
              const Gap(16),
              Text(
                tr(LocaleKeys.about_the_app),
                style: context.textTheme.titleLarge,
              ),
              const Gap(16),
              SettingsTile(
                onTap: () {},
                icon: Assets.icons.privacyPolicy,
                text: tr(LocaleKeys.privacy_policy),
              ),
              const Gap(8),
              SettingsTile(
                onTap: () => Nav.to(Pages.create_provider),
                icon: Assets.icons.providersOutlined,
                text: tr(LocaleKeys.become_a_provider),
              ),
              const Gap(8),
              SettingsTile(
                onTap: () {},
                icon: Assets.icons.contactUs,
                text: tr(LocaleKeys.contact_us),
              ),
              const Gap(8),
              SettingsTile(
                onTap: () {},
                icon: Assets.icons.aboutUs,
                text: tr(LocaleKeys.about_us),
              ),
              const Divider(),
              const Gap(16),
              Text(
                tr(LocaleKeys.settings),
                style: context.textTheme.titleLarge,
              ),
              const Gap(16),
              SettingsTile(
                onTap: () {
                  Get.find<AppBuilder>().updateLocale();
                },
                icon: Assets.icons.languages,
                text: tr(LocaleKeys.language),
              ),
              SettingsTile(
                onTap: () {},
                icon: Assets.icons.lock,
                text: tr(LocaleKeys.change_password),
              ),
              const Divider(),
              ElevatedButton(
                onPressed: () => Get.find<AppBuilder>().logout(),
                child: Text(tr(LocaleKeys.login)),
              ),
            ],
          ),
          const Blur(
            child: SizedBox(height: kImageSize / 2, width: double.infinity),
          ),
        ],
      ),
    );
  }
}
