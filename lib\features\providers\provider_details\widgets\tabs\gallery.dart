import 'package:flutter/material.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/image.dart';

class GalleryTab extends StatelessWidget {
  final List<String> images;

  const GalleryTab({super.key, required this.images});

  @override
  Widget build(BuildContext context) {
    return SliverList(
      delegate: SliverChildListDelegate([
        SizedBox(
          height: 360,
          child: CarouselView.weighted(
            flexWeights: const [1, 8, 1],
            itemSnapping: true,
            shape: const LinearBorder(),
            children:
                images
                    .map(
                      (e) => AppImage(
                        path: e,
                        height: 300,
                        decoration: BoxDecoration(
                          color: context.appColorScheme.primaryColor,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(
                            color: context.appColorScheme.primaryColor,
                          ),
                        ),
                      ),
                    )
                    .toList(),
          ),
        ),
      ]),
    );
  }
}
