import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/models/selection.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/fields/dropdown_filed.dart';
import 'package:tredo/core/widgets/general/fields/field_error.dart';
import 'package:tredo/core/widgets/general/fields/field_loading.dart';

import 'controller.dart';

class CategoriesSubcategoriesWidget extends StatelessWidget {
  final int? initialCategory;
  final int? initialSubcategory;
  final void Function(Selection? value)? onCategoryChanged;
  final void Function(Selection? value)? onSubcategoryChanged;
  final bool supportNone;
  final bool isRequired;

  const CategoriesSubcategoriesWidget({
    super.key,
    this.initialCategory,
    this.initialSubcategory,
    this.onCategoryChanged,
    this.onSubcategoryChanged,
    this.supportNone = false,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(
      CategoriesSubcategoriesController(
        initialSubcategory: initialSubcategory,
        initialCategory: initialCategory,
      ),
    );

    return Row(
      spacing: 12,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: ObsListBuilder(
            obs: controller.categories,
            loader: (context) => const FieldLoadingWidget(),
            errorBuilder:
                (context, error) => FieldErrorWidget(
                  error: error,
                  onRefresh: controller.refreshCategories,
                ),
            builder: (context, categories) {
              return AppDropdownField(
                hint: tr(LocaleKeys.category),
                value: categories.firstWhereOrNull(
                  (element) => element.id == initialCategory,
                ),
                validator: isRequired ? Validator.notNull : null,
                icon: Assets.icons.categoriesSearch,
                selections: categories,
                supportNone: supportNone,
                onChanged: (value) {
                  onCategoryChanged?.call(value);
                  controller.selectedCategoryId =
                      value?.id ?? Selection.none.id;
                },
              );
            },
          ),
        ),
        Expanded(
          child: Obx(() {
            if (controller.selectedSubcategoryId == Selection.none.id) {
              onSubcategoryChanged?.call(null);
            }

            return AnimatedSwitcher(
              duration: 300.milliseconds,
              child:
                  controller.selectedCategoryId == Selection.none.id
                      ? const SizedBox()
                      : ObsListBuilder(
                        obs: controller.subcategories,
                        loader: (context) => const FieldLoadingWidget(),
                        errorBuilder:
                            (context, error) => FieldErrorWidget(
                              error: error,
                              onRefresh: controller.refreshSubcategories,
                            ),
                        builder: (context, subcategories) {
                          return AppDropdownField(
                            hint: tr(LocaleKeys.subcategory),
                            value: subcategories.firstWhereOrNull(
                              (element) =>
                                  element.id ==
                                  controller.selectedSubcategoryId,
                            ),
                            validator: isRequired ? Validator.notNull : null,
                            icon: Assets.icons.building,
                            supportNone: supportNone,
                            selections: subcategories,
                            onChanged: (value) {
                              onSubcategoryChanged?.call(value);
                            },
                          );
                        },
                      ),
            );
          }),
        ),
      ],
    );
  }
}
