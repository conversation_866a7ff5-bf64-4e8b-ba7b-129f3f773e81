import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/models/selection.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/fields/dropdown_filed.dart';
import 'package:tredo/core/widgets/general/fields/field_error.dart';
import 'package:tredo/core/widgets/general/fields/field_loading.dart';

import 'controller.dart';

class StatesCitiesWidget extends StatelessWidget {
  final int? initialState;
  final int? initialCity;
  final void Function(Selection? value)? onStateChanged;
  final void Function(Selection? value)? onCityChanged;
  final bool supportNone;
  final bool isRequired;

  const StatesCitiesWidget({
    super.key,
    this.initialState,
    this.initialCity,
    this.onStateChanged,
    this.onCityChanged,
    this.supportNone = false,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(
      StatesCitiesController(
        initialCity: initialCity,
        initialState: initialState,
      ),
    );

    return Row(
      spacing: 12,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: ObsListBuilder(
            obs: controller.states,
            loader: (context) => const FieldLoadingWidget(),
            errorBuilder:
                (context, error) => FieldErrorWidget(
                  error: error,
                  onRefresh: controller.refreshStates,
                ),
            builder: (context, states) {
              return AppDropdownField(
                hint: tr(LocaleKeys.state),
                value: states.firstWhereOrNull(
                  (element) => element.id == initialState,
                ),
                validator: isRequired ? Validator.notNull : null,
                icon: Assets.icons.earth,
                selections: states,
                supportNone: supportNone,
                onChanged: (value) {
                  onStateChanged?.call(value);
                  controller.selectedStateId = value?.id ?? Selection.none.id;
                },
              );
            },
          ),
        ),
        Expanded(
          child: Obx(() {
            if (controller.selectedCityId == Selection.none.id) {
              onCityChanged?.call(null);
            }

            return AnimatedSwitcher(
              duration: 300.milliseconds,
              child:
                  controller.selectedStateId == Selection.none.id
                      ? const SizedBox()
                      : ObsListBuilder(
                        obs: controller.cities,
                        loader: (context) => const FieldLoadingWidget(),
                        errorBuilder:
                            (context, error) => FieldErrorWidget(
                              error: error,
                              onRefresh: controller.refreshCities,
                            ),
                        builder: (context, cities) {
                          return AppDropdownField(
                            hint: tr(LocaleKeys.city),
                            value: cities.firstWhereOrNull(
                              (element) =>
                                  element.id == controller.selectedCityId,
                            ),
                            validator: isRequired ? Validator.notNull : null,
                            icon: Assets.icons.building,
                            supportNone: supportNone,
                            selections: cities,
                            onChanged: (value) {
                              onCityChanged?.call(value);
                            },
                          );
                        },
                      ),
            );
          }),
        ),
      ],
    );
  }
}
