import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;

import 'status.dart';
import 'widgets/toast.dart';

const Duration _kToastDuration = Duration(seconds: 5);

class Toast {
  // overlay toast using OverlayEntry
  static show({
    String? title,
    required String message,
    ToastStatus status = ToastStatus.fail,
    Duration duration = _kToastDuration,
  }) async {
    OverlayEntry toast = OverlayEntry(
      builder:
          (context) => Stack(
            children: [
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: AppToast(
                    title: title,
                    message: message,
                    status: status,
                  ),
                ),
              ),
            ],
          ),
    );
    Overlay.of(Get.overlayContext!).insert(toast);
    await Future.delayed(duration);
    toast.remove();
  }
}
